{
  "name": "Codex",
  "build": {
    "dockerfile": "Dockerfile",
    "context": "..",
    "platform": "linux/arm64"
  },

  /* Force VS Code to run the container as arm64 in
     case your host is x86 (or vice-versa). */
  "runArgs": ["--platform=linux/arm64"],

  "containerEnv": {
    "RUST_BACKTRACE": "1",
    "CARGO_TARGET_DIR": "${containerWorkspaceFolder}/codex-rs/target-arm64"
  },

  "remoteUser": "dev",
  "customizations": {
    "vscode": {
      "settings": {
          "terminal.integrated.defaultProfile.linux": "bash"
      },
      "extensions": [
          "rust-lang.rust-analyzer"
      ],
    }
  }
}
